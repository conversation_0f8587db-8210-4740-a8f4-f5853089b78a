import{_ as v,a as r,o as a,c,b as n,d as o,w as s,F as y,f as F,m as V,e as _}from"./index-7d3731bf.js";const h={name:"CreatePost",data(){return{postForm:{title:"",content:"",tags:[]},rules:{title:[{required:!0,message:"请输入标题",trigger:"blur"},{min:5,max:50,message:"标题长度在5到50个字符",trigger:"blur"}],content:[{required:!0,message:"请输入内容",trigger:"blur"},{min:10,message:"内容不能少于10个字符",trigger:"blur"}]},commonTags:["Java基础","Spring Boot","Spring Cloud","MyBatis","MySQL","Redis","多线程","分布式","微服务","性能优化","设计模式","源码分析"],submitting:!1}},methods:{submitPost(){this.$refs.postForm.validate(async m=>{if(m){this.submitting=!0;try{await new Promise(e=>setTimeout(e,1e3)),this.$message.success("发布成功！"),this.$router.push("/community/discussion")}catch{this.$message.error("发布失败，请重试")}finally{this.submitting=!1}}})},cancel(){this.$router.back()}}},w={class:"create-post"},x={class:"post-form"},C={class:"form-actions"};function k(m,e,B,P,t,u){const p=r("el-input"),i=r("el-form-item"),f=r("el-option"),g=r("el-select"),d=r("el-button"),b=r("el-form");return a(),c("div",w,[e[6]||(e[6]=n("div",{class:"page-header"},[n("h2",null,"发布帖子")],-1)),n("div",x,[o(b,{model:t.postForm,rules:t.rules,ref:"postForm","label-position":"top"},{default:s(()=>[o(i,{label:"标题",prop:"title"},{default:s(()=>[o(p,{modelValue:t.postForm.title,"onUpdate:modelValue":e[0]||(e[0]=l=>t.postForm.title=l),placeholder:"请输入帖子标题（5-50字）",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1}),o(i,{label:"内容",prop:"content"},{default:s(()=>[o(p,{type:"textarea",modelValue:t.postForm.content,"onUpdate:modelValue":e[1]||(e[1]=l=>t.postForm.content=l),placeholder:"请输入帖子内容",rows:10,maxlength:"10000","show-word-limit":""},null,8,["modelValue"])]),_:1}),o(i,{label:"标签"},{default:s(()=>[o(g,{modelValue:t.postForm.tags,"onUpdate:modelValue":e[2]||(e[2]=l=>t.postForm.tags=l),multiple:"",filterable:"","allow-create":"","default-first-option":"",placeholder:"请选择或输入标签"},{default:s(()=>[(a(!0),c(y,null,F(t.commonTags,l=>(a(),V(f,{key:l,label:l,value:l},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),e[3]||(e[3]=n("div",{class:"form-tip"},"最多可添加5个标签",-1))]),_:1}),o(i,null,{default:s(()=>[n("div",C,[o(d,{type:"primary",onClick:u.submitPost,loading:t.submitting},{default:s(()=>e[4]||(e[4]=[_("发布帖子")])),_:1},8,["onClick","loading"]),o(d,{onClick:u.cancel},{default:s(()=>e[5]||(e[5]=[_("取消")])),_:1},8,["onClick"])])]),_:1})]),_:1},8,["model","rules"])])])}const N=v(h,[["render",k],["__scopeId","data-v-6733fba2"]]);export{N as default};
