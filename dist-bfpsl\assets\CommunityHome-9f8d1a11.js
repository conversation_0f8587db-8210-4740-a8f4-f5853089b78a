import{_ as k,x as w,y as A,z as b,a as c,o,c as a,b as e,F as u,f as p,m,w as r,d as l,A as g,t as n,e as x}from"./index-7d3731bf.js";const B={name:"CommunityHome",components:{ChatDotRound:w,Opportunity:A,Share:b},data(){return{circles:[{id:1,name:"面试圈",description:"分享面试经验，交流面试技巧，助力求职成功",icon:"ChatDotRound",route:"/community/interview"},{id:2,name:"技能圈",description:"探讨业务与技术栈，分享学习资源，共同提升技能",icon:"Opportunity",route:"/community/skill"},{id:3,name:"讨论圈",description:"交流职业发展，探讨Java生态，分享成长经验",icon:"Share",route:"/community/discussion"}],recentActivities:[{id:1,username:"张三",userAvatar:"https://placeholder.pics/svg/50",type:"发表了文章",content:"Spring Boot 项目实战经验分享",time:"10分钟前"},{id:2,username:"李四",userAvatar:"https://placeholder.pics/svg/50",type:"评论了文章",content:"这个解决方案非常实用，感谢分享！",time:"30分钟前"}]}},methods:{enterCircle(i){const s=this.$router.resolve(i);window.open(s.href,"_blank")},openArticle(i){const s=this.$router.resolve(`/community/interview/article/${i}`);window.open(s.href,"_blank")}}},$={class:"community-home"},D={class:"container"},S={class:"circles-grid"},N={class:"circle-content"},V={class:"circle-name"},z={class:"circle-description"},F={class:"recent-activities"},H={class:"activities-list"},O=["onClick"],R={class:"activity-avatar"},E={class:"activity-content"},I={class:"activity-header"},J={class:"username"},L={class:"activity-type"},T={class:"time"},j={class:"activity-text"};function q(i,s,G,K,d,_){const v=c("el-icon"),h=c("el-button"),y=c("el-card"),f=c("el-avatar");return o(),a("div",$,[s[2]||(s[2]=e("div",{class:"page-header"},[e("h1",null,"职业成长社区"),e("p",null,"分享经验，交流学习，共同成长")],-1)),e("div",D,[e("div",S,[(o(!0),a(u,null,p(d.circles,t=>(o(),m(y,{key:t.id,class:"circle-card"},{default:r(()=>[e("div",N,[l(v,{size:40,class:"circle-icon"},{default:r(()=>[(o(),m(g(t.icon)))]),_:2},1024),e("h3",V,n(t.name),1),e("p",z,n(t.description),1),l(h,{type:"primary",class:"enter-button",onClick:C=>_.enterCircle(t.route)},{default:r(()=>s[0]||(s[0]=[x("进入圈子")])),_:2},1032,["onClick"])])]),_:2},1024))),128))]),e("div",F,[s[1]||(s[1]=e("h2",{class:"section-title"},"最新动态",-1)),e("div",H,[(o(!0),a(u,null,p(d.recentActivities,t=>(o(),a("div",{key:t.id,class:"activity-item",onClick:C=>_.openArticle(t.id),style:{cursor:"pointer"}},[e("div",R,[l(f,{src:t.userAvatar},null,8,["src"])]),e("div",E,[e("div",I,[e("span",J,n(t.username),1),e("span",L,n(t.type),1),e("span",T,n(t.time),1)]),e("div",j,n(t.content),1)])],8,O))),128))])])])])}const P=k(B,[["render",q],["__scopeId","data-v-b5ececc1"]]);export{P as default};
