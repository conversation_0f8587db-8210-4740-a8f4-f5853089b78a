!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).FloatingUIUtils={})}(this,(function(t){"use strict";const e=["top","right","bottom","left"],n=["start","end"],o=e.reduce(((t,e)=>t.concat(e,e+"-"+n[0],e+"-"+n[1])),[]),i=Math.min,r=Math.max,c=Math.round,u=Math.floor,f={left:"right",right:"left",bottom:"top",top:"bottom"},s={start:"end",end:"start"};function a(t){return t.split("-")[0]}function l(t){return t.split("-")[1]}function g(t){return"x"===t?"y":"x"}function p(t){return"y"===t?"height":"width"}function d(t){return["top","bottom"].includes(a(t))?"y":"x"}function m(t){return g(d(t))}function h(t){return t.replace(/start|end/g,(t=>s[t]))}function x(t){return t.replace(/left|right|bottom|top/g,(t=>f[t]))}function b(t){return{top:0,right:0,bottom:0,left:0,...t}}t.alignments=n,t.clamp=function(t,e,n){return r(t,i(e,n))},t.createCoords=t=>({x:t,y:t}),t.evaluate=function(t,e){return"function"==typeof t?t(e):t},t.expandPaddingObject=b,t.floor=u,t.getAlignment=l,t.getAlignmentAxis=m,t.getAlignmentSides=function(t,e,n){void 0===n&&(n=!1);const o=l(t),i=m(t),r=p(i);let c="x"===i?o===(n?"end":"start")?"right":"left":"start"===o?"bottom":"top";return e.reference[r]>e.floating[r]&&(c=x(c)),[c,x(c)]},t.getAxisLength=p,t.getExpandedPlacements=function(t){const e=x(t);return[h(t),e,h(e)]},t.getOppositeAlignmentPlacement=h,t.getOppositeAxis=g,t.getOppositeAxisPlacements=function(t,e,n,o){const i=l(t);let r=function(t,e,n){const o=["left","right"],i=["right","left"],r=["top","bottom"],c=["bottom","top"];switch(t){case"top":case"bottom":return n?e?i:o:e?o:i;case"left":case"right":return e?r:c;default:return[]}}(a(t),"start"===n,o);return i&&(r=r.map((t=>t+"-"+i)),e&&(r=r.concat(r.map(h)))),r},t.getOppositePlacement=x,t.getPaddingObject=function(t){return"number"!=typeof t?b(t):{top:t,right:t,bottom:t,left:t}},t.getSide=a,t.getSideAxis=d,t.max=r,t.min=i,t.placements=o,t.rectToClientRect=function(t){const{x:e,y:n,width:o,height:i}=t;return{width:o,height:i,top:n,left:e,right:e+o,bottom:n+i,x:e,y:n}},t.round=c,t.sides=e}));
