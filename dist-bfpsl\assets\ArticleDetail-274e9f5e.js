import{_ as y,a as u,o as m,c as d,b as e,d as o,w as r,t as i,e as n,F as C,f,h as g}from"./index-7d3731bf.js";const w={name:"ArticleDetail",data(){return{article:{id:1,title:"字节跳动Java开发三轮技术面经验分享",category:{name:"技术面试",type:"success"},company:"互联网大厂",experience:"1-3年",time:"2024-03-15",author:{name:"技术小王",avatar:"https://placeholder.pics/svg/50"},views:1234,comments:56,likes:89,content:`
          <h2>面试准备</h2>
          <p>在准备字节跳动的面试之前，我主要做了以下几个方面的准备：</p>
          <ul>
            <li>复习Java核心知识点</li>
            <li>系统设计相关的案例学习</li>
            <li>算法题目练习</li>
            <li>项目经验梳理</li>
          </ul>

          <h2>技术面试流程</h2>
          <h3>第一轮：基础面试</h3>
          <p>主要考察Java基础知识、数据结构和算法：</p>
          <ul>
            <li>Java集合框架的实现原理</li>
            <li>JVM内存模型和垃圾回收机制</li>
            <li>算法题：实现一个LRU缓存</li>
          </ul>

          <h3>第二轮：项目深度面试</h3>
          <p>重点讨论项目经验和技术选型：</p>
          <ul>
            <li>项目架构设计的考虑</li>
            <li>性能优化的实践</li>
            <li>分布式系统的设计</li>
          </ul>

          <h3>第三轮：系统设计面试</h3>
          <p>考察系统设计能力：</p>
          <ul>
            <li>设计一个短链接服务</li>
            <li>高并发场景的处理方案</li>
            <li>数据一致性的保证</li>
          </ul>

          <h2>面试总结</h2>
          <p>整个面试过程非常注重实际问题的解决能力，建议：</p>
          <ul>
            <li>准备充分的项目案例</li>
            <li>理解技术选型的背景</li>
            <li>掌握系统设计的方法论</li>
          </ul>
        `},comments:[{id:1,username:"程序员小张",avatar:"https://placeholder.pics/svg/50",content:"分享得很详细，对我准备面试很有帮助！",time:"2024-03-15 10:30",likes:12},{id:2,username:"Java学习者",avatar:"https://placeholder.pics/svg/50",content:"请问系统设计环节具体考察哪些知识点？",time:"2024-03-15 11:15",likes:8}],relatedArticles:[{id:2,title:"阿里巴巴Java开发面试全流程复盘",views:2156,comments:89},{id:3,title:"系统设计面试题详解 - 高并发场景",views:1567,comments:98}],newComment:"",hasMoreComments:!0}},methods:{handleLike(){this.$message.success("点赞成功")},handleShare(){this.$message.success("分享链接已复制")},submitComment(){if(!this.newComment.trim()){this.$message.warning("请输入评论内容");return}this.$message.success("评论发表成功"),this.newComment=""},likeComment(a){a.likes++},replyComment(a){this.newComment=`@${a.username} `},loadMoreComments(){this.hasMoreComments=!1},goToArticle(a){this.$router.push(`/community/interview/article/${a}`)},async fetchArticleDetail(){const a=this.$route.params.id;console.log("获取文章ID:",a)}},created(){this.fetchArticleDetail()}},x={class:"article-detail"},A={class:"article-container"},b={class:"article-header"},M={class:"meta-tags"},V={class:"title"},D={class:"article-info"},J={class:"author-info"},L={class:"author-name"},T={class:"publish-info"},N={class:"time"},B={class:"stats"},S={class:"stat-item"},z={class:"stat-item"},F={class:"stat-item"},H=["innerHTML"],I={class:"article-footer"},U={class:"actions"},E={class:"comments-section"},R={class:"section-title"},j={class:"comment-input"},q={class:"comment-actions"},G={class:"comments-list"},K={class:"comment-header"},O={class:"commenter-info"},P={class:"commenter-name"},Q={class:"comment-time"},W={class:"comment-content"},X={class:"comment-actions"},Y=["onClick"],Z=["onClick"],$={key:0,class:"load-more"},ee={class:"related-articles"},te={class:"related-list"},se=["onClick"],ie={class:"related-title"},le={class:"related-meta"};function ne(a,t,oe,ae,s,c){const h=u("el-tag"),p=u("el-avatar"),_=u("el-button"),k=u("el-input");return m(),d("div",x,[e("div",A,[e("div",b,[e("div",M,[o(h,{type:s.article.category.type},{default:r(()=>[n(i(s.article.category.name),1)]),_:1},8,["type"]),o(h,{effect:"plain"},{default:r(()=>[n(i(s.article.company),1)]),_:1}),o(h,{effect:"plain"},{default:r(()=>[n(i(s.article.experience),1)]),_:1})]),e("h1",V,i(s.article.title),1),e("div",D,[e("div",J,[o(p,{size:32,src:s.article.author.avatar},null,8,["src"]),e("span",L,i(s.article.author.name),1)]),e("div",T,[e("span",N,"发布于 "+i(s.article.time),1),e("span",B,[e("span",S,[t[1]||(t[1]=e("i",{class:"el-icon-view"},null,-1)),n(" "+i(s.article.views)+" 阅读 ",1)]),e("span",z,[t[2]||(t[2]=e("i",{class:"el-icon-chat-dot-round"},null,-1)),n(" "+i(s.article.comments)+" 评论 ",1)]),e("span",F,[t[3]||(t[3]=e("i",{class:"el-icon-star-off"},null,-1)),n(" "+i(s.article.likes)+" 点赞 ",1)])])])])]),e("div",{class:"article-content",innerHTML:s.article.content},null,8,H),e("div",I,[e("div",U,[o(_,{type:"primary",plain:"",onClick:c.handleLike},{default:r(()=>t[4]||(t[4]=[e("i",{class:"el-icon-star-off"},null,-1),n(" 点赞 ")])),_:1},8,["onClick"]),o(_,{type:"primary",plain:"",onClick:c.handleShare},{default:r(()=>t[5]||(t[5]=[e("i",{class:"el-icon-share"},null,-1),n(" 分享 ")])),_:1},8,["onClick"])])]),e("div",E,[e("h2",R,"评论 ("+i(s.article.comments)+")",1),e("div",j,[o(k,{type:"textarea",modelValue:s.newComment,"onUpdate:modelValue":t[0]||(t[0]=l=>s.newComment=l),rows:3,placeholder:"写下你的评论..."},null,8,["modelValue"]),e("div",q,[o(_,{type:"primary",onClick:c.submitComment},{default:r(()=>t[6]||(t[6]=[n("发表评论")])),_:1},8,["onClick"])])]),e("div",G,[(m(!0),d(C,null,f(s.comments,l=>(m(),d("div",{key:l.id,class:"comment-item"},[e("div",K,[e("div",O,[o(p,{size:24,src:l.avatar},null,8,["src"]),e("span",P,i(l.username),1)]),e("span",Q,i(l.time),1)]),e("div",W,i(l.content),1),e("div",X,[e("span",{class:"action-item",onClick:v=>c.likeComment(l)},[t[7]||(t[7]=e("i",{class:"el-icon-star-off"},null,-1)),n(" "+i(l.likes),1)],8,Y),e("span",{class:"action-item",onClick:v=>c.replyComment(l)},t[8]||(t[8]=[e("i",{class:"el-icon-chat-dot-round"},null,-1),n(" 回复 ")]),8,Z)])]))),128))]),s.hasMoreComments?(m(),d("div",$,[o(_,{plain:"",onClick:c.loadMoreComments},{default:r(()=>t[9]||(t[9]=[n("加载更多评论")])),_:1},8,["onClick"])])):g("",!0)])]),e("div",ee,[t[10]||(t[10]=e("h2",{class:"section-title"},"相关文章",-1)),e("div",te,[(m(!0),d(C,null,f(s.relatedArticles,l=>(m(),d("div",{key:l.id,class:"related-item",onClick:v=>c.goToArticle(l.id)},[e("h3",ie,i(l.title),1),e("div",le,[e("span",null,i(l.views)+" 阅读",1),e("span",null,i(l.comments)+" 评论",1)])],8,se))),128))])])])}const re=y(w,[["render",ne],["__scopeId","data-v-6b503835"]]);export{re as default};
