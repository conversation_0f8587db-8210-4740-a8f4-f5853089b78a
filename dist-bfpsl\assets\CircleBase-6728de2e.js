import{_ as b,a as i,o as c,c as d,b as t,t as l,d as n,w as h,e as u,h as x,F as g,f,m as V}from"./index-7d3731bf.js";const z={name:"CircleBase",props:{title:{type:String,required:!0},readOnly:{type:Boolean,default:!0}},data(){return{sortBy:"latest",searchQuery:"",currentPage:1,pageSize:10,total:0,posts:[]}},computed:{filteredPosts(){let o=[...this.posts];if(this.searchQuery){const e=this.searchQuery.toLowerCase();o=o.filter(a=>a.title.toLowerCase().includes(e)||a.excerpt.toLowerCase().includes(e)||a.tags.some(v=>v.toLowerCase().includes(e)))}switch(this.sortBy){case"comments":o.sort((e,a)=>a.comments-e.comments);break;case"likes":o.sort((e,a)=>a.likes-e.likes);break;default:o.sort((e,a)=>new Date(a.createTime)-new Date(e.createTime))}return o}},methods:{createPost(){this.$router.push("/community/discussion/create")},viewPost(o){this.$router.push(`/community/${this.$route.params.type}/post/${o}`)},handlePageChange(o){this.currentPage=o,this.fetchPosts(o)},async fetchPosts(o){this.posts=[{id:1,title:"分享一道Spring Boot面试题的解题思路",excerpt:"最近在面试中遇到一道关于Spring Boot自动配置的问题，这里分享下我的解题思路...",authorName:"技术达人",authorAvatar:"https://placeholder.pics/svg/50",createTime:"2024-03-10 14:30",views:1234,comments:56,likes:89,tags:["Spring Boot","面试题","经验分享"]},{id:2,title:"如何优化Java程序的性能？",excerpt:"本文将从JVM调优、代码优化、数据库优化等多个角度，详细讲解Java程序性能优化的方法...",authorName:"Java专家",authorAvatar:"https://placeholder.pics/svg/50",createTime:"2024-03-09 16:45",views:2341,comments:123,likes:234,tags:["性能优化","JVM","最佳实践"]}],this.total=100}},created(){this.fetchPosts(1)}},N={class:"circle-base"},S={class:"circle-header"},T={key:0,class:"circle-actions"},J={class:"post-filters"},L={class:"posts-list"},Q={class:"post-header"},q={class:"post-author"},A={class:"author-name"},D={class:"post-time"},F={class:"post-content"},M=["onClick"],O={class:"post-excerpt"},U={class:"post-footer"},E={class:"post-stats"},I={class:"stat-item"},j={class:"stat-item"},G={class:"stat-item"},H={class:"post-tags"},K={class:"pagination"};function R(o,e,a,v,r,_){const y=i("el-button"),p=i("el-option"),C=i("el-select"),k=i("el-input"),w=i("el-avatar"),B=i("el-tag"),P=i("el-pagination");return c(),d("div",N,[t("div",S,[t("h2",null,l(a.title),1),a.readOnly?x("",!0):(c(),d("div",T,[n(y,{type:"primary",onClick:_.createPost},{default:h(()=>e[2]||(e[2]=[u("发布帖子")])),_:1},8,["onClick"])]))]),t("div",J,[n(C,{modelValue:r.sortBy,"onUpdate:modelValue":e[0]||(e[0]=s=>r.sortBy=s),placeholder:"排序方式",size:"small"},{default:h(()=>[n(p,{label:"最新发布",value:"latest"}),n(p,{label:"最多评论",value:"comments"}),n(p,{label:"最多点赞",value:"likes"})]),_:1},8,["modelValue"]),n(k,{modelValue:r.searchQuery,"onUpdate:modelValue":e[1]||(e[1]=s=>r.searchQuery=s),placeholder:"搜索帖子","prefix-icon":"el-icon-search",size:"small",clearable:""},null,8,["modelValue"])]),t("div",L,[(c(!0),d(g,null,f(_.filteredPosts,s=>(c(),d("div",{key:s.id,class:"post-card"},[t("div",Q,[t("div",q,[n(w,{src:s.authorAvatar,size:"small"},null,8,["src"]),t("span",A,l(s.authorName),1)]),t("span",D,l(s.createTime),1)]),t("div",F,[t("h3",{class:"post-title",onClick:m=>_.viewPost(s.id)},l(s.title),9,M),t("p",O,l(s.excerpt),1)]),t("div",U,[t("div",E,[t("span",I,[e[3]||(e[3]=t("i",{class:"el-icon-view"},null,-1)),u(" "+l(s.views),1)]),t("span",j,[e[4]||(e[4]=t("i",{class:"el-icon-chat-dot-square"},null,-1)),u(" "+l(s.comments),1)]),t("span",G,[e[5]||(e[5]=t("i",{class:"el-icon-star-off"},null,-1)),u(" "+l(s.likes),1)])]),t("div",H,[(c(!0),d(g,null,f(s.tags,m=>(c(),V(B,{key:m,size:"small",effect:"plain"},{default:h(()=>[u(l(m),1)]),_:2},1024))),128))])])]))),128))]),t("div",K,[n(P,{background:"",layout:"prev, pager, next",total:r.total,"page-size":r.pageSize,"current-page":r.currentPage,onCurrentChange:_.handlePageChange},null,8,["total","page-size","current-page","onCurrentChange"])])])}const X=b(z,[["render",R],["__scopeId","data-v-f8b21001"]]);export{X as C};
