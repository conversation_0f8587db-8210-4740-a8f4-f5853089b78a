import{_,a,o as d,c as u,b as s,d as t,w as n,n as c,e as l}from"./index-7d3731bf.js";const p={name:"ResourcesView"},v={class:"resources"},m={class:"content-wrapper"},f={class:"sidebar"},w={class:"main-content"};function b(o,e,g,k,V,$){const r=a("router-link"),i=a("router-view");return d(),u("div",v,[e[2]||(e[2]=s("div",{class:"page-header"},[s("h1",null,"内推资源"),s("p",null,"专业的求职内推服务，助您找到理想工作")],-1)),s("div",m,[s("div",f,[t(r,{to:"/resources/general",class:c(["nav-link",{active:o.$route.path==="/resources/general"}])},{default:n(()=>e[0]||(e[0]=[l(" 普通内推 ")])),_:1},8,["class"]),t(r,{to:"/resources/targeted",class:c(["nav-link",{active:o.$route.path==="/resources/targeted"}])},{default:n(()=>e[1]||(e[1]=[l(" 定向内推 ")])),_:1},8,["class"])]),s("div",w,[t(i)])])])}const x=_(p,[["render",b],["__scopeId","data-v-0053bb91"]]);export{x as default};
