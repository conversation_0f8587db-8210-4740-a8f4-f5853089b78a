import{_ as b,a as m,o as f,c as v,b as s,d as e,w as n,F as k,f as V,e as p,t as r}from"./index-7d3731bf.js";const z={name:"InterviewCircle",data(){return{filters:{category:"",company:"",experience:"",search:""},currentPage:1,pageSize:5,totalArticles:0,articles:[]}},computed:{filteredArticles(){const i=this.articles.filter(c=>{const l=!this.filters.category||this.filters.category==="tech"&&c.category.name==="技术面试"||this.filters.category==="hr"&&c.category.name==="HR面试"||this.filters.category==="prep"&&c.category.name==="面试准备"||this.filters.category==="offer"&&c.category.name==="offer选择",u=!this.filters.company||c.company===this.getCompanyName(this.filters.company),d=!this.filters.experience||c.experience===this.getExperienceName(this.filters.experience),o=!this.filters.search||c.title.toLowerCase().includes(this.filters.search.toLowerCase())||c.summary.toLowerCase().includes(this.filters.search.toLowerCase());return l&&u&&d&&o});this.totalArticles=i.length;const t=(this.currentPage-1)*this.pageSize,g=t+this.pageSize;return i.slice(t,g)}},methods:{getCompanyName(i){return{"big-tech":"互联网大厂",unicorn:"独角兽",foreign:"外企","state-owned":"国企"}[i]||""},getExperienceName(i){return{fresh:"应届生",junior:"1-3年",mid:"3-5年",senior:"5年以上"}[i]||""},handlePageChange(i){this.currentPage=i,window.scrollTo({top:0,behavior:"smooth"})},async fetchArticles(){const i=[{id:1,title:"字节跳动Java开发三轮技术面经验分享",summary:"分享了我在字节跳动面试Java开发岗位的完整经历，包括面试准备、技术问题、系统设计等各个环节的详细内容。",category:{name:"技术面试",type:"success"},company:"互联网大厂",experience:"1-3年",time:"2024-03-15",author:{name:"技术小王",avatar:"https://placeholder.pics/svg/50"},views:1234,comments:56,likes:89},{id:2,title:"HR面试技巧 - 薪资谈判案例分析",summary:"整理了最近的HR面试经验，重点分享薪资谈判技巧和注意事项，希望能帮助大家在面试中争取到更好的待遇。",category:{name:"HR面试",type:"warning"},company:"独角兽",experience:"3-5年",time:"2024-03-14",author:{name:"面试达人",avatar:"https://placeholder.pics/svg/50"},views:856,comments:32,likes:67},{id:3,title:"阿里巴巴Java开发面试全流程复盘",summary:"详细记录了阿里巴巴Java开发岗位的面试流程，包括笔试、四轮技术面和HR面，重点分析考察重点和答题技巧。",category:{name:"技术面试",type:"success"},company:"互联网大厂",experience:"3-5年",time:"2024-03-13",author:{name:"Java高手",avatar:"https://placeholder.pics/svg/50"},views:2156,comments:89,likes:235},{id:4,title:"应届生面试准备指南 - 从零开始的面试之路",summary:"作为应届生求职者，如何准备Java开发面试？本文从简历准备、基础知识、项目经验等多个维度进行详细指导。",category:{name:"面试准备",type:"info"},company:"互联网大厂",experience:"应届生",time:"2024-03-12",author:{name:"校招专家",avatar:"https://placeholder.pics/svg/50"},views:3678,comments:156,likes:420},{id:5,title:"外企VS国内大厂 - 个人选择经验分享",summary:"在收到多个offer后的选择思考，对比了外企和国内大厂在技术栈、团队文化、薪资福利等方面的差异。",category:{name:"offer选择",type:"primary"},company:"外企",experience:"3-5年",time:"2024-03-11",author:{name:"职场导师",avatar:"https://placeholder.pics/svg/50"},views:1567,comments:98,likes:145},{id:6,title:"系统设计面试题详解 - 高并发场景",summary:"整理了最常见的系统设计面试题，重点分析高并发场景下的解决方案，包括实际案例和架构演进过程。",category:{name:"技术面试",type:"success"},company:"互联网大厂",experience:"5年以上",time:"2024-03-10",author:{name:"架构师张",avatar:"https://placeholder.pics/svg/50"},views:2890,comments:134,likes:367},{id:7,title:"HR面试常见问题及应对策略",summary:"总结了HR面试中最常见的问题类型，并提供了详细的应对策略和话术建议，帮助候选人更好地展现自己。",category:{name:"HR面试",type:"warning"},company:"国企",experience:"1-3年",time:"2024-03-09",author:{name:"HR专家",avatar:"https://placeholder.pics/svg/50"},views:1234,comments:67,likes:98},{id:8,title:"独角兽公司面试经验分享",summary:"分享在某独角兽公司的面试经历，包括技术考核重点、团队文化特点，以及如何准备此类公司的面试。",category:{name:"技术面试",type:"success"},company:"独角兽",experience:"1-3年",time:"2024-03-08",author:{name:"程序员小李",avatar:"https://placeholder.pics/svg/50"},views:945,comments:45,likes:76},{id:9,title:"面试准备清单 - 技术面试必备知识点",summary:"整理了Java开发面试中必备的知识点清单，包括Java基础、框架、中间件、数据库等各个方面的重点内容。",category:{name:"面试准备",type:"info"},company:"互联网大厂",experience:"1-3年",time:"2024-03-07",author:{name:"面试教练",avatar:"https://placeholder.pics/svg/50"},views:4567,comments:234,likes:567},{id:10,title:"如何选择最适合自己的offer",summary:"从薪资、发展空间、工作内容、团队氛围等多个维度，分析如何做出最适合自己的offer选择。",category:{name:"offer选择",type:"primary"},company:"独角兽",experience:"3-5年",time:"2024-03-06",author:{name:"职业规划师",avatar:"https://placeholder.pics/svg/50"},views:2345,comments:123,likes:289}];this.articles=i},goToArticle(i){this.$router.push(`/community/interview/article/${i}`)}},created(){this.fetchArticles()},watch:{filters:{deep:!0,handler(){this.currentPage=1}}}},H={class:"interview-circle"},R={class:"circle-header"},A={class:"header-content"},J={class:"main-content"},T={class:"filter-section"},N={class:"articles-section"},P=["onClick"],S={class:"article-header"},L={class:"article-meta"},E={class:"article-time"},I={class:"article-title"},U={class:"article-summary"},B={class:"article-footer"},j={class:"author-info"},D={class:"author-name"},F={class:"article-stats"},q={class:"stat-item"},G={class:"stat-item"},K={class:"stat-item"},M={class:"pagination-section"};function O(i,t,g,c,l,u){const d=m("el-tag"),o=m("el-option"),y=m("el-select"),h=m("el-col"),_=m("el-input"),w=m("el-row"),x=m("el-avatar"),C=m("el-pagination");return f(),v("div",H,[s("div",R,[s("div",A,[t[5]||(t[5]=s("h1",null,"面试经验圈子",-1)),e(d,{type:"success",size:"large"},{default:n(()=>t[4]||(t[4]=[p("只读讨论")])),_:1})]),t[6]||(t[6]=s("p",{class:"description"},"这里收集了Java开发者的面试经验分享，帮助你更好地准备面试",-1))]),s("div",J,[s("div",T,[e(w,{gutter:20},{default:n(()=>[e(h,{span:6},{default:n(()=>[e(y,{modelValue:l.filters.category,"onUpdate:modelValue":t[0]||(t[0]=a=>l.filters.category=a),placeholder:"文章分类",clearable:""},{default:n(()=>[e(o,{label:"技术面试",value:"tech"}),e(o,{label:"HR面试",value:"hr"}),e(o,{label:"面试准备",value:"prep"}),e(o,{label:"offer选择",value:"offer"})]),_:1},8,["modelValue"])]),_:1}),e(h,{span:6},{default:n(()=>[e(y,{modelValue:l.filters.company,"onUpdate:modelValue":t[1]||(t[1]=a=>l.filters.company=a),placeholder:"公司类型",clearable:""},{default:n(()=>[e(o,{label:"互联网大厂",value:"big-tech"}),e(o,{label:"独角兽",value:"unicorn"}),e(o,{label:"外企",value:"foreign"}),e(o,{label:"国企",value:"state-owned"})]),_:1},8,["modelValue"])]),_:1}),e(h,{span:6},{default:n(()=>[e(y,{modelValue:l.filters.experience,"onUpdate:modelValue":t[2]||(t[2]=a=>l.filters.experience=a),placeholder:"工作经验",clearable:""},{default:n(()=>[e(o,{label:"应届生",value:"fresh"}),e(o,{label:"1-3年",value:"junior"}),e(o,{label:"3-5年",value:"mid"}),e(o,{label:"5年以上",value:"senior"})]),_:1},8,["modelValue"])]),_:1}),e(h,{span:6},{default:n(()=>[e(_,{modelValue:l.filters.search,"onUpdate:modelValue":t[3]||(t[3]=a=>l.filters.search=a),placeholder:"搜索文章","prefix-icon":"el-icon-search"},null,8,["modelValue"])]),_:1})]),_:1})]),s("div",N,[(f(!0),v(k,null,V(u.filteredArticles,a=>(f(),v("div",{key:a.id,class:"article-card",onClick:Q=>u.goToArticle(a.id)},[s("div",S,[s("div",L,[e(d,{size:"small",type:a.category.type},{default:n(()=>[p(r(a.category.name),1)]),_:2},1032,["type"]),e(d,{size:"small",effect:"plain"},{default:n(()=>[p(r(a.company),1)]),_:2},1024),e(d,{size:"small",effect:"plain"},{default:n(()=>[p(r(a.experience),1)]),_:2},1024)]),s("span",E,r(a.time),1)]),s("h2",I,r(a.title),1),s("p",U,r(a.summary),1),s("div",B,[s("div",j,[e(x,{size:24,src:a.author.avatar},null,8,["src"]),s("span",D,r(a.author.name),1)]),s("div",F,[s("span",q,[t[7]||(t[7]=s("i",{class:"el-icon-view"},null,-1)),p(" "+r(a.views),1)]),s("span",G,[t[8]||(t[8]=s("i",{class:"el-icon-chat-dot-round"},null,-1)),p(" "+r(a.comments),1)]),s("span",K,[t[9]||(t[9]=s("i",{class:"el-icon-star-off"},null,-1)),p(" "+r(a.likes),1)])])])],8,P))),128)),s("div",M,[e(C,{background:"",layout:"prev, pager, next",total:l.totalArticles,"page-size":l.pageSize,"current-page":l.currentPage,onCurrentChange:u.handlePageChange},null,8,["total","page-size","current-page","onCurrentChange"])])])])])}const X=b(z,[["render",O],["__scopeId","data-v-798df98e"]]);export{X as default};
