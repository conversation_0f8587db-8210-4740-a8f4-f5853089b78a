import{_ as V,u as j,r as w,a as l,o as n,c as r,b as e,d as v,w as d,g as i,h as b,i as y,F as x,f as B,j as N}from"./index-7d3731bf.js";const S={class:"career-paths"},P={class:"container"},h={class:"filter-container"},D={class:"filter-label"},F={key:0,class:"career-stage-section"},T={class:"services-grid"},q={key:1,class:"career-stage-section"},A={class:"services-grid"},E={key:2,class:"career-stage-section"},I={class:"services-grid"},L={class:"faq-section"},M={__name:"CareerPathsView",setup(O){const f=j(),m=[{value:"all",label:"全部"},{value:"campus",label:"在校期间求职"},{value:"junior",label:"毕业1-5年求职"},{value:"senior",label:"毕业5年以上求职"}],o=w("all");function p(c){return o.value==="all"||o.value===c}function t(c){f.push({path:`/service-detail/${c}`})}return(c,a)=>{const g=l("el-icon"),k=l("el-option"),C=l("el-select"),u=l("el-collapse-item"),$=l("el-collapse");return n(),r("div",S,[e("div",P,[a[33]||(a[33]=e("div",{class:"top-spacer"},null,-1)),e("div",h,[e("div",D,[v(g,null,{default:d(()=>[v(y(N))]),_:1}),a[13]||(a[13]=e("span",null,"职业阶段筛选:",-1))]),v(C,{modelValue:o.value,"onUpdate:modelValue":a[0]||(a[0]=s=>o.value=s),placeholder:"选择职业阶段",class:"stage-filter"},{default:d(()=>[(n(),r(x,null,B(m,s=>v(k,{key:s.value,label:s.label,value:s.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),p("campus")?(n(),r("section",F,[a[18]||(a[18]=e("div",{class:"section-header"},[e("h2",{class:"stage-title"},"在校期间求职"),e("p",{class:"stage-description"},"定制化的校园招聘服务，帮助在校生获得理想实习和全职机会")],-1)),e("div",T,[e("div",{class:"service-card",onClick:a[1]||(a[1]=s=>t("campus-cv"))},a[14]||(a[14]=[i('<div class="service-card-header" data-v-35532ea4><div class="service-icon" data-v-35532ea4><i class="el-icon-document" data-v-35532ea4></i></div><h3 data-v-35532ea4>简历优化</h3></div><p data-v-35532ea4>专业简历修改和优化，突出学术成果和实习经历，提高简历通过率</p><div class="service-footer" data-v-35532ea4><span class="service-price" data-v-35532ea4></span><button class="service-button" data-v-35532ea4>了解详情</button></div>',3)])),e("div",{class:"service-card",onClick:a[2]||(a[2]=s=>t("campus-interview"))},a[15]||(a[15]=[i('<div class="service-card-header" data-v-35532ea4><div class="service-icon" data-v-35532ea4><i class="el-icon-headset" data-v-35532ea4></i></div><h3 data-v-35532ea4>面试辅导</h3></div><p data-v-35532ea4>模拟校招面试环节，针对性提升面试技巧，包括自我介绍、项目经验包装等</p><div class="service-footer" data-v-35532ea4><span class="service-price" data-v-35532ea4></span><button class="service-button" data-v-35532ea4>了解详情</button></div>',3)])),e("div",{class:"service-card",onClick:a[3]||(a[3]=s=>t("campus-referral"))},a[16]||(a[16]=[i('<div class="service-card-header" data-v-35532ea4><div class="service-icon" data-v-35532ea4><i class="el-icon-position" data-v-35532ea4></i></div><h3 data-v-35532ea4>校招内推</h3></div><p data-v-35532ea4>针对应届生的大厂内推服务，覆盖BAT、TMD等知名互联网公司</p><div class="service-footer" data-v-35532ea4><span class="service-price" data-v-35532ea4></span><button class="service-button" data-v-35532ea4>了解详情</button></div>',3)])),e("div",{class:"service-card",onClick:a[4]||(a[4]=s=>t("campus-projects"))},a[17]||(a[17]=[i('<div class="service-card-header" data-v-35532ea4><div class="service-icon" data-v-35532ea4><i class="el-icon-folder" data-v-35532ea4></i></div><h3 data-v-35532ea4>项目经验构建</h3></div><p data-v-35532ea4>提供实战项目经验和指导，帮助在校生积累有竞争力的项目经验</p><div class="service-footer" data-v-35532ea4><span class="service-price" data-v-35532ea4></span><button class="service-button" data-v-35532ea4>了解详情</button></div>',3)]))])])):b("",!0),p("junior")?(n(),r("section",q,[a[23]||(a[23]=e("div",{class:"section-header"},[e("h2",{class:"stage-title"},"毕业1-5年求职"),e("p",{class:"stage-description"},"针对职场新人和中级开发者的进阶服务，助力职业快速发展")],-1)),e("div",A,[e("div",{class:"service-card",onClick:a[5]||(a[5]=s=>t("junior-cv"))},a[19]||(a[19]=[i('<div class="service-card-header" data-v-35532ea4><div class="service-icon" data-v-35532ea4><i class="el-icon-document-checked" data-v-35532ea4></i></div><h3 data-v-35532ea4>高级简历定制</h3></div><p data-v-35532ea4>突出项目成果和技术能力，针对目标公司定制简历内容和风格</p><div class="service-footer" data-v-35532ea4><span class="service-price" data-v-35532ea4></span><button class="service-button" data-v-35532ea4>了解详情</button></div>',3)])),e("div",{class:"service-card",onClick:a[6]||(a[6]=s=>t("junior-skill"))},a[20]||(a[20]=[i('<div class="service-card-header" data-v-35532ea4><div class="service-icon" data-v-35532ea4><i class="el-icon-cpu" data-v-35532ea4></i></div><h3 data-v-35532ea4>技术栈评估与提升</h3></div><p data-v-35532ea4>评估当前技术能力，制定个性化学习计划，突破技术瓶颈</p><div class="service-footer" data-v-35532ea4><span class="service-price" data-v-35532ea4></span><button class="service-button" data-v-35532ea4>了解详情</button></div>',3)])),e("div",{class:"service-card",onClick:a[7]||(a[7]=s=>t("junior-promotion"))},a[21]||(a[21]=[i('<div class="service-card-header" data-v-35532ea4><div class="service-icon" data-v-35532ea4><i class="el-icon-data-line" data-v-35532ea4></i></div><h3 data-v-35532ea4>晋升路径规划</h3></div><p data-v-35532ea4>分析当前职位发展空间，制定明确晋升目标和行动计划</p><div class="service-footer" data-v-35532ea4><span class="service-price" data-v-35532ea4></span><button class="service-button" data-v-35532ea4>了解详情</button></div>',3)])),e("div",{class:"service-card",onClick:a[8]||(a[8]=s=>t("junior-bigtech"))},a[22]||(a[22]=[i('<div class="service-card-header" data-v-35532ea4><div class="service-icon" data-v-35532ea4><i class="el-icon-office-building" data-v-35532ea4></i></div><h3 data-v-35532ea4>大厂冲刺计划</h3></div><p data-v-35532ea4>全方位大厂面试准备，包括算法训练、系统设计和技术面试模拟</p><div class="service-footer" data-v-35532ea4><span class="service-price" data-v-35532ea4></span><button class="service-button" data-v-35532ea4>了解详情</button></div>',3)]))])])):b("",!0),p("senior")?(n(),r("section",E,[a[28]||(a[28]=e("div",{class:"section-header"},[e("h2",{class:"stage-title"},"毕业5年以上求职"),e("p",{class:"stage-description"},"面向资深开发者和技术管理者的高端服务，助力职业转型与突破")],-1)),e("div",I,[e("div",{class:"service-card",onClick:a[9]||(a[9]=s=>t("senior-management"))},a[24]||(a[24]=[i('<div class="service-card-header" data-v-35532ea4><div class="service-icon" data-v-35532ea4><i class="el-icon-user" data-v-35532ea4></i></div><h3 data-v-35532ea4>管理岗转型</h3></div><p data-v-35532ea4>从技术专家到管理者的全方位转型指导，包括团队管理、沟通技巧等</p><div class="service-footer" data-v-35532ea4><span class="service-price" data-v-35532ea4></span><button class="service-button" data-v-35532ea4>了解详情</button></div>',3)])),e("div",{class:"service-card",onClick:a[10]||(a[10]=s=>t("senior-architect"))},a[25]||(a[25]=[i('<div class="service-card-header" data-v-35532ea4><div class="service-icon" data-v-35532ea4><i class="el-icon-set-up" data-v-35532ea4></i></div><h3 data-v-35532ea4>架构师晋升</h3></div><p data-v-35532ea4>系统架构设计能力提升，大型系统案例分析，架构评审实战</p><div class="service-footer" data-v-35532ea4><span class="service-price" data-v-35532ea4></span><button class="service-button" data-v-35532ea4>了解详情</button></div>',3)])),e("div",{class:"service-card",onClick:a[11]||(a[11]=s=>t("senior-stateowned"))},a[26]||(a[26]=[i('<div class="service-card-header" data-v-35532ea4><div class="service-icon" data-v-35532ea4><i class="el-icon-bank-card" data-v-35532ea4></i></div><h3 data-v-35532ea4>央国企转型</h3></div><p data-v-35532ea4>互联网转央国企求职指导，稳定职业道路规划，工作生活平衡提升</p><div class="service-footer" data-v-35532ea4><span class="service-price" data-v-35532ea4></span><button class="service-button" data-v-35532ea4>了解详情</button></div>',3)])),e("div",{class:"service-card",onClick:a[12]||(a[12]=s=>t("senior-international"))},a[27]||(a[27]=[i('<div class="service-card-header" data-v-35532ea4><div class="service-icon" data-v-35532ea4><i class="el-icon-earth" data-v-35532ea4></i></div><h3 data-v-35532ea4>海外职位内推</h3></div><p data-v-35532ea4>针对资深开发者的海外工作机会，包括新加坡、美国等地区职位</p><div class="service-footer" data-v-35532ea4><span class="service-price" data-v-35532ea4></span><button class="service-button" data-v-35532ea4>了解详情</button></div>',3)]))])])):b("",!0),e("section",L,[a[32]||(a[32]=e("h2",{class:"section-title"},"常见问题",-1)),v($,null,{default:d(()=>[v(u,{title:"如何选择适合自己的职业规划服务？",name:"1"},{default:d(()=>a[29]||(a[29]=[e("p",null,"我们建议您根据自己当前的职业阶段和发展目标选择相应的服务。如果您对选择有疑问，可以预约我们的职业顾问进行免费咨询，获取个性化建议。",-1)])),_:1}),v(u,{title:"服务流程是怎样的？",name:"2"},{default:d(()=>a[30]||(a[30]=[e("p",null,"一般包括：1）需求沟通与评估；2）制定个性化服务方案；3）专家一对一指导；4）服务交付与反馈；5）后续跟进与调整。具体流程会根据服务类型有所调整。",-1)])),_:1}),v(u,{title:"如果对服务不满意可以退款吗？",name:"3"},{default:d(()=>a[31]||(a[31]=[e("p",null,"我们提供服务质量保障。如果您对服务不满意，可以在服务完成后7天内申请退款，我们会根据服务完成情况和具体原因进行评估。",-1)])),_:1})]),_:1})])])])}}},U=V(M,[["__scopeId","data-v-35532ea4"]]);export{U as default};
