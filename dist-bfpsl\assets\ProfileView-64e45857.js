import{_ as G,r as g,l as Q,a as i,o as d,c as m,b as e,d as s,w as a,e as c,t as n,F as y,f as h,n as j,m as q,h as H}from"./index-7d3731bf.js";const K={class:"profile"},O={class:"container"},R={class:"profile-info"},W={class:"profile-header"},X={class:"avatar-container"},Y={class:"profile-form"},Z={class:"form-row"},$={class:"form-row"},ee={class:"form-actions"},te={class:"learning-plan"},le={class:"plan-header"},se={class:"plan-progress"},ae={class:"progress-header"},oe={class:"progress-percentage"},ne={class:"learning-items"},ie={class:"item-header"},re={class:"item-progress"},de={class:"item-info"},ue={class:"info-row"},pe={class:"info-value"},me={class:"info-row"},ce={class:"info-value"},ve={class:"info-row"},_e={class:"info-value"},fe={class:"item-actions"},ge={class:"career-development"},be={class:"path-selection"},Ve={class:"path-card card"},ye={class:"path-header"},he={class:"path-info"},ke={class:"path-timeline"},we={class:"timeline-content"},xe={class:"timeline-status"},De={key:0,class:"completion-date"},Fe={class:"career-skills"},Ue={class:"skills-card card"},Se={class:"skill-goals"},Ce={class:"skill-goal-header"},Je={class:"skill-level"},Me={class:"skill-resources"},Le=["href"],Pe={__name:"ProfileView",setup(ze){const D=g("info"),o=g({name:"张三",gender:"male",birthdate:new Date(1990,0,1),email:"<EMAIL>",phone:"13800138000",position:"Java中级开发工程师",experience:"3-5",city:"北京",goal:"3年内成为资深后端开发工程师，5年内成为架构师。"}),b=g([{title:"Spring Boot高级实战",status:"in-progress",progress:45,startDate:"2023-03-15",endDate:"2023-06-30",type:"在线课程"},{title:"Java并发编程",status:"completed",progress:100,startDate:"2023-01-10",endDate:"2023-02-28",type:"视频教程"},{title:"Spring Cloud微服务架构",status:"not-started",progress:0,startDate:"2023-07-01",endDate:"2023-09-30",type:"实战项目"},{title:"MySQL性能优化",status:"in-progress",progress:75,startDate:"2023-02-15",endDate:"2023-04-30",type:"技术书籍"}]),F=Q(()=>{if(b.value.length===0)return 0;const r=b.value.reduce((t,u)=>t+u.progress,0);return Math.round(r/b.value.length)}),k=g({title:"Java后端开发工程师",description:"从初级开发到架构师的职业发展路径",stages:[{title:"初级开发工程师",description:"掌握Java基础、Spring框架等核心技能，能够独立完成功能模块开发",status:"completed",completionDate:"2022-05-15"},{title:"中级开发工程师",description:"深入理解Java并发、微服务架构等进阶技能，能够设计和实现复杂功能",status:"current"},{title:"高级开发工程师",description:"掌握系统架构设计、性能优化等高级技能，能够主导技术方案设计",status:"future"},{title:"架构师",description:"精通分布式系统、高并发架构等专家级技能，能够设计和实现企业级架构",status:"future"}]}),C=g([{name:"Spring Cloud微服务",currentLevel:2,progress:40,recommendation:"深入学习Spring Cloud组件和微服务架构设计",resources:[{title:"Spring Cloud实战教程",link:"#"},{title:"微服务设计模式",link:"#"}]},{name:"分布式系统",currentLevel:1.5,progress:30,recommendation:"学习分布式系统原理和常见问题解决方案",resources:[{title:"分布式系统实践指南",link:"#"},{title:"分布式架构课程",link:"#"}]},{name:"高并发编程",currentLevel:3,progress:60,recommendation:"深入学习Java并发编程模型和高并发场景优化",resources:[{title:"Java并发编程实战",link:"#"},{title:"高性能Java应用",link:"#"}]}]);function U(r){return r+"%"}function J(r){return{"not-started":"未开始","in-progress":"进行中",completed:"已完成"}[r]||r}function M(r){return{completed:"已完成",current:"进行中",future:"规划中"}[r]||r}function L(r){return{completed:"success",current:"primary",future:"info"}[r]||""}return(r,t)=>{const u=i("el-button"),_=i("el-input"),p=i("el-form-item"),v=i("el-option"),S=i("el-select"),P=i("el-date-picker"),z=i("el-form"),w=i("el-tab-pane"),x=i("el-progress"),T=i("el-tag"),B=i("el-timeline-item"),N=i("el-timeline"),E=i("el-rate"),I=i("el-tabs");return d(),m("div",K,[t[30]||(t[30]=e("section",{class:"page-header"},[e("div",{class:"container"},[e("h1",null,"个人中心"),e("p",null,"管理您的个人信息、学习计划和职业发展")])],-1)),e("div",O,[s(I,{modelValue:D.value,"onUpdate:modelValue":t[9]||(t[9]=l=>D.value=l),class:"profile-tabs"},{default:a(()=>[s(w,{label:"个人信息",name:"info"},{default:a(()=>[e("div",R,[e("div",W,[e("div",X,[t[11]||(t[11]=e("img",{src:"https://placeholder.pics/svg/150/4F46E5/FFFFFF/user",alt:"用户头像",class:"profile-avatar"},null,-1)),s(u,{size:"small",class:"avatar-edit-btn"},{default:a(()=>t[10]||(t[10]=[c("修改")])),_:1})]),t[12]||(t[12]=e("div",{class:"profile-summary"},[e("h2",null,"张三"),e("p",{class:"user-title"},"Java中级开发工程师"),e("div",{class:"user-stats"},[e("div",{class:"stat-item"},[e("div",{class:"stat-value"},"65%"),e("div",{class:"stat-label"},"技能完成度")]),e("div",{class:"stat-item"},[e("div",{class:"stat-value"},"12"),e("div",{class:"stat-label"},"学习项目")]),e("div",{class:"stat-item"},[e("div",{class:"stat-value"},"5"),e("div",{class:"stat-label"},"证书")])])],-1))]),e("div",Y,[t[16]||(t[16]=e("h3",null,"基本信息",-1)),s(z,{"label-position":"top",model:o.value,class:"user-form"},{default:a(()=>[s(p,{label:"姓名"},{default:a(()=>[s(_,{modelValue:o.value.name,"onUpdate:modelValue":t[0]||(t[0]=l=>o.value.name=l)},null,8,["modelValue"])]),_:1}),e("div",Z,[s(p,{label:"性别"},{default:a(()=>[s(S,{modelValue:o.value.gender,"onUpdate:modelValue":t[1]||(t[1]=l=>o.value.gender=l),placeholder:"请选择"},{default:a(()=>[s(v,{label:"男",value:"male"}),s(v,{label:"女",value:"female"}),s(v,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),s(p,{label:"出生日期"},{default:a(()=>[s(P,{modelValue:o.value.birthdate,"onUpdate:modelValue":t[2]||(t[2]=l=>o.value.birthdate=l),type:"date",placeholder:"选择日期"},null,8,["modelValue"])]),_:1})]),s(p,{label:"电子邮箱"},{default:a(()=>[s(_,{modelValue:o.value.email,"onUpdate:modelValue":t[3]||(t[3]=l=>o.value.email=l)},null,8,["modelValue"])]),_:1}),s(p,{label:"手机号码"},{default:a(()=>[s(_,{modelValue:o.value.phone,"onUpdate:modelValue":t[4]||(t[4]=l=>o.value.phone=l)},null,8,["modelValue"])]),_:1}),t[15]||(t[15]=e("h3",null,"职业信息",-1)),s(p,{label:"当前职位"},{default:a(()=>[s(_,{modelValue:o.value.position,"onUpdate:modelValue":t[5]||(t[5]=l=>o.value.position=l)},null,8,["modelValue"])]),_:1}),e("div",$,[s(p,{label:"工作年限"},{default:a(()=>[s(S,{modelValue:o.value.experience,"onUpdate:modelValue":t[6]||(t[6]=l=>o.value.experience=l),placeholder:"请选择"},{default:a(()=>[s(v,{label:"应届毕业生",value:"0"}),s(v,{label:"1-3年",value:"1-3"}),s(v,{label:"3-5年",value:"3-5"}),s(v,{label:"5-10年",value:"5-10"}),s(v,{label:"10年以上",value:"10+"})]),_:1},8,["modelValue"])]),_:1}),s(p,{label:"所在城市"},{default:a(()=>[s(_,{modelValue:o.value.city,"onUpdate:modelValue":t[7]||(t[7]=l=>o.value.city=l)},null,8,["modelValue"])]),_:1})]),s(p,{label:"职业目标"},{default:a(()=>[s(_,{type:"textarea",modelValue:o.value.goal,"onUpdate:modelValue":t[8]||(t[8]=l=>o.value.goal=l),rows:"3"},null,8,["modelValue"])]),_:1}),e("div",ee,[s(u,{type:"primary"},{default:a(()=>t[13]||(t[13]=[c("保存信息")])),_:1}),s(u,null,{default:a(()=>t[14]||(t[14]=[c("取消")])),_:1})])]),_:1},8,["model"])])])]),_:1}),s(w,{label:"学习计划",name:"learning"},{default:a(()=>[e("div",te,[e("div",le,[t[18]||(t[18]=e("h2",null,"我的学习计划",-1)),s(u,{type:"primary"},{default:a(()=>t[17]||(t[17]=[c("添加学习项目")])),_:1})]),e("div",se,[e("div",ae,[t[19]||(t[19]=e("h3",null,"总体进度",-1)),e("span",oe,n(F.value)+"%",1)]),s(x,{percentage:F.value,"stroke-width":20},null,8,["percentage"])]),e("div",ne,[(d(!0),m(y,null,h(b.value,(l,f)=>(d(),m("div",{key:f,class:"learning-item card"},[e("div",ie,[e("h4",null,n(l.title),1),e("div",{class:j(["item-tag",l.status])},n(J(l.status)),3)]),e("div",re,[s(x,{percentage:l.progress,format:U},null,8,["percentage"])]),e("div",de,[e("div",ue,[t[20]||(t[20]=e("span",{class:"info-label"},"开始时间:",-1)),e("span",pe,n(l.startDate),1)]),e("div",me,[t[21]||(t[21]=e("span",{class:"info-label"},"预计完成:",-1)),e("span",ce,n(l.endDate),1)]),e("div",ve,[t[22]||(t[22]=e("span",{class:"info-label"},"资源类型:",-1)),e("span",_e,n(l.type),1)])]),e("div",fe,[s(u,{type:"primary",size:"small"},{default:a(()=>t[23]||(t[23]=[c("继续学习")])),_:1}),s(u,{size:"small"},{default:a(()=>t[24]||(t[24]=[c("查看详情")])),_:1})])]))),128))])])]),_:1}),s(w,{label:"职业发展",name:"career"},{default:a(()=>[e("div",ge,[e("div",be,[t[26]||(t[26]=e("h2",null,"我的职业路径",-1)),e("div",Ve,[e("div",ye,[e("div",he,[e("h3",null,n(k.value.title),1),e("p",null,n(k.value.description),1)]),s(u,{size:"small"},{default:a(()=>t[25]||(t[25]=[c("切换路径")])),_:1})]),e("div",ke,[s(N,null,{default:a(()=>[(d(!0),m(y,null,h(k.value.stages,(l,f)=>(d(),q(B,{key:f,type:l.status==="completed"?"success":"primary",color:l.status==="completed"?"#67C23A":l.status==="current"?"#409EFF":"#909399"},{default:a(()=>[e("div",we,[e("h4",null,n(l.title),1),e("p",null,n(l.description),1),e("div",xe,[s(T,{type:L(l.status)},{default:a(()=>[c(n(M(l.status)),1)]),_:2},1032,["type"]),l.status==="completed"?(d(),m("span",De,"完成于 "+n(l.completionDate),1)):H("",!0)])])]),_:2},1032,["type","color"]))),128))]),_:1})])])]),e("div",Fe,[t[29]||(t[29]=e("h2",null,"技能发展建议",-1)),e("div",Ue,[t[28]||(t[28]=e("h3",null,"提升这些技能以达到下一阶段",-1)),e("div",Se,[(d(!0),m(y,null,h(C.value,(l,f)=>(d(),m("div",{key:f,class:"skill-goal"},[e("div",Ce,[e("h4",null,n(l.name),1),e("div",Je,[t[27]||(t[27]=c(" 当前: ")),s(E,{modelValue:l.currentLevel,"onUpdate:modelValue":V=>l.currentLevel=V,disabled:"","text-color":"#ff9900"},null,8,["modelValue","onUpdate:modelValue"])])]),s(x,{percentage:l.progress,"stroke-width":10,format:U},null,8,["percentage"]),e("p",null,n(l.recommendation),1),e("div",Me,[(d(!0),m(y,null,h(l.resources,(V,A)=>(d(),m("a",{key:A,href:V.link,target:"_blank",class:"resource-link"},n(V.title),9,Le))),128))])]))),128))])])])])]),_:1})]),_:1},8,["modelValue"])])])}}},Be=G(Pe,[["__scopeId","data-v-55d8ecb7"]]);export{Be as default};
