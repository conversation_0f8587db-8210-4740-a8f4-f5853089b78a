import{_ as v,r as x,a as r,o as d,c as p,b as t,d as i,w as l,e as o,F as y,f as _,t as n}from"./index-7d3731bf.js";const g={class:"home"},m={class:"hero"},w={class:"container"},L={class:"section"},F={class:"container"},b={class:"grid grid-3"},M={class:"card feature-card"},k={class:"card feature-card"},Z={class:"card feature-card"},B={class:"section testimonials-section"},V={class:"container"},C={class:"testimonials"},E={class:"testimonial-card"},J={class:"testimonial-avatar"},D=["src","alt"],H={class:"testimonial-content"},N={class:"testimonial-text"},O={class:"testimonial-author"},j={class:"section cta-section"},I={class:"container"},S={class:"cta-card"},T={class:"cta-buttons"},q={__name:"HomeView",setup(z){x("");const u=[{name:"张明",position:"Java高级架构师",avatar:"https://placeholder.pics/svg/100/4F46E5/FFFFFF/user1",text:"这个平台的职业路径规划给了我清晰的方向，从初级开发到架构师，每一步都有详细的技能指导。"},{name:"李婷",position:"DevOps工程师",avatar:"https://placeholder.pics/svg/100/4F46E5/FFFFFF/user2",text:"技能评估功能帮助我发现了自己的短板，根据推荐的学习路径，我顺利转型为DevOps工程师。"},{name:"王强",position:"后端技术主管",avatar:"https://placeholder.pics/svg/100/4F46E5/FFFFFF/user3",text:"通过这个平台的学习资源和社区交流，我不仅提升了技术能力，还成功晋升为团队的技术主管。"}];return(A,s)=>{const a=r("el-carousel-item"),c=r("el-carousel"),f=r("router-link");return d(),p("div",g,[t("section",m,[t("div",w,[i(c,{interval:5e3,height:"400px",class:"hero-carousel"},{default:l(()=>[i(a,null,{default:l(()=>s[0]||(s[0]=[t("div",{class:"carousel-item"},[t("div",{class:"carousel-image career-planning"},[t("svg",{viewBox:"0 0 400 300",xmlns:"http://www.w3.org/2000/svg",class:"illustration"},[t("path",{d:"M130,250 L190,150 L250,250 Z",fill:"#ffffff",opacity:"0.2"}),t("path",{d:"M160,250 L220,100 L280,250 Z",fill:"#ffffff",opacity:"0.3"}),t("circle",{cx:"220",cy:"90",r:"15",fill:"#ffffff",opacity:"0.5"}),t("path",{d:"M100,250 C140,230 180,230 220,250 C260,230 300,230 340,250 L340,270 L100,270 Z",fill:"#ffffff",opacity:"0.2"}),t("path",{d:"M120,170 L120,250 L140,250 L140,190 Z",fill:"#ffffff",opacity:"0.4"}),t("path",{d:"M160,200 L160,250 L180,250 L180,200 Z",fill:"#ffffff",opacity:"0.3"}),t("path",{d:"M200,150 L200,250 L220,250 L220,150 Z",fill:"#ffffff",opacity:"0.5"}),t("path",{d:"M240,180 L240,250 L260,250 L260,180 Z",fill:"#ffffff",opacity:"0.4"}),t("path",{d:"M280,220 L280,250 L300,250 L300,220 Z",fill:"#ffffff",opacity:"0.3"})])]),t("div",{class:"carousel-content"},[t("h2",{class:"carousel-title"},"职业规划"),t("p",{class:"carousel-subtitle"},"从初级开发到架构师，规划您的技术成长之路")])],-1)])),_:1}),i(a,null,{default:l(()=>s[1]||(s[1]=[t("div",{class:"carousel-item"},[t("div",{class:"carousel-image job-referral"},[t("svg",{viewBox:"0 0 400 300",xmlns:"http://www.w3.org/2000/svg",class:"illustration"},[t("rect",{x:"100",y:"80",width:"200",height:"140",rx:"5",fill:"#ffffff",opacity:"0.3"}),t("rect",{x:"120",y:"100",width:"160",height:"10",rx:"2",fill:"#ffffff",opacity:"0.5"}),t("rect",{x:"120",y:"120",width:"120",height:"10",rx:"2",fill:"#ffffff",opacity:"0.5"}),t("rect",{x:"120",y:"140",width:"140",height:"10",rx:"2",fill:"#ffffff",opacity:"0.5"}),t("rect",{x:"120",y:"160",width:"100",height:"10",rx:"2",fill:"#ffffff",opacity:"0.5"}),t("rect",{x:"120",y:"180",width:"80",height:"10",rx:"2",fill:"#ffffff",opacity:"0.5"}),t("circle",{cx:"280",cy:"150",r:"25",fill:"#ffffff",opacity:"0.4"}),t("path",{d:"M270,150 L280,160 L290,140",stroke:"#ffffff","stroke-width":"3",fill:"none"})])]),t("div",{class:"carousel-content"},[t("h2",{class:"carousel-title"},"内推资源"),t("p",{class:"carousel-subtitle"},"获取优质企业内推机会，加速您的求职进程")])],-1)])),_:1}),i(a,null,{default:l(()=>s[2]||(s[2]=[t("div",{class:"carousel-item"},[t("div",{class:"carousel-image resume-resources"},[t("svg",{viewBox:"0 0 400 300",xmlns:"http://www.w3.org/2000/svg",class:"illustration"},[t("rect",{x:"120",y:"60",width:"160",height:"200",rx:"5",fill:"#ffffff",opacity:"0.3"}),t("rect",{x:"140",y:"80",width:"40",height:"40",rx:"20",fill:"#ffffff",opacity:"0.5"}),t("rect",{x:"140",y:"130",width:"120",height:"8",rx:"2",fill:"#ffffff",opacity:"0.5"}),t("rect",{x:"140",y:"145",width:"100",height:"8",rx:"2",fill:"#ffffff",opacity:"0.5"}),t("rect",{x:"140",y:"170",width:"120",height:"2",rx:"1",fill:"#ffffff",opacity:"0.3"}),t("rect",{x:"140",y:"180",width:"120",height:"2",rx:"1",fill:"#ffffff",opacity:"0.3"}),t("rect",{x:"140",y:"190",width:"120",height:"2",rx:"1",fill:"#ffffff",opacity:"0.3"}),t("rect",{x:"140",y:"200",width:"120",height:"2",rx:"1",fill:"#ffffff",opacity:"0.3"}),t("rect",{x:"140",y:"210",width:"120",height:"2",rx:"1",fill:"#ffffff",opacity:"0.3"}),t("rect",{x:"140",y:"220",width:"80",height:"2",rx:"1",fill:"#ffffff",opacity:"0.3"}),t("circle",{cx:"300",cy:"240",r:"30",fill:"#ffffff",opacity:"0.2"}),t("path",{d:"M290,240 L310,240 M300,230 L300,250",stroke:"#ffffff","stroke-width":"3"})])]),t("div",{class:"carousel-content"},[t("h2",{class:"carousel-title"},"简历强化资源"),t("p",{class:"carousel-subtitle"},"专业简历模板与指导，提升面试通过率")])],-1)])),_:1})]),_:1})])]),t("section",L,[t("div",F,[s[15]||(s[15]=t("h2",{class:"section-title"},"服务项目",-1)),t("div",b,[t("div",M,[s[4]||(s[4]=t("div",{class:"card-icon"},[t("i",{class:"el-icon-map-location"})],-1)),s[5]||(s[5]=t("h3",null,"职业路线图",-1)),s[6]||(s[6]=t("p",null,"从初级开发到架构师，清晰展示Java开发者不同发展方向和路径",-1)),i(f,{to:"/career-paths",class:"btn btn-outline"},{default:l(()=>s[3]||(s[3]=[o("探索路径")])),_:1})]),t("div",k,[s[8]||(s[8]=t("div",{class:"card-icon"},[t("i",{class:"el-icon-data-line"})],-1)),s[9]||(s[9]=t("h3",null,"热门技能趋势",-1)),s[10]||(s[10]=t("p",null,"紧跟市场需求，展示Java生态圈最新技术趋势和热门框架",-1)),i(f,{to:"/skill-assessment",class:"btn btn-outline"},{default:l(()=>s[7]||(s[7]=[o("查看趋势")])),_:1})]),t("div",Z,[s[12]||(s[12]=t("div",{class:"card-icon"},[t("i",{class:"el-icon-user"})],-1)),s[13]||(s[13]=t("h3",null,"职位内推",-1)),s[14]||(s[14]=t("p",null,"基于您的兴趣和技能水平，智能推荐合适的职位机会",-1)),i(f,{to:"/profile",class:"btn btn-outline"},{default:l(()=>s[11]||(s[11]=[o("获取推荐")])),_:1})])])])]),t("section",B,[t("div",V,[s[16]||(s[16]=t("h2",{class:"section-title"},"他们的成功故事",-1)),t("div",C,[i(c,{interval:4e3,type:"card"},{default:l(()=>[(d(),p(y,null,_(u,(e,h)=>i(a,{key:h},{default:l(()=>[t("div",E,[t("div",J,[t("img",{src:e.avatar,alt:e.name},null,8,D)]),t("div",H,[t("p",N,n(e.text),1),t("div",O,[t("strong",null,n(e.name),1),t("span",null,n(e.position),1)])])])]),_:2},1024)),64))]),_:1})])])]),t("section",j,[t("div",I,[t("div",S,[s[19]||(s[19]=t("h2",null,"准备好开启您的Java职业规划了吗？",-1)),s[20]||(s[20]=t("p",null,"创建账户，获取个性化的职业规划和技能提升建议",-1)),t("div",T,[i(f,{to:"/profile",class:"btn btn-primary"},{default:l(()=>s[17]||(s[17]=[o("开始规划")])),_:1}),i(f,{to:"/learning-resources",class:"btn btn-outline"},{default:l(()=>s[18]||(s[18]=[o("探索资源")])),_:1})])])])])])}}},K=v(q,[["__scopeId","data-v-fcd4d241"]]);export{K as default};
